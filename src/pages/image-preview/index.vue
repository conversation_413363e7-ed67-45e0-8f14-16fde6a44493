<template>
    <view class="image-preview-container">
        <!-- 顶部导航栏 -->
        <view class="nav-bar">
            <view class="back-button" @click="handleBack">
                <uni-icons type="closeempty" size="24" color="#fff"></uni-icons>
            </view>
            <view class="counter">
                <text>{{ currentIndex + 1 }}/{{ images.length }}</text>
            </view>
            <text></text>
        </view>

        <!-- 图片轮播区域 -->
        <swiper class="swiper-container" :current="currentIndex" @change="handleSwiperChange" :indicator-dots="false"
            :autoplay="false" :circular="true">
            <swiper-item v-for="(item, index) in images" :key="index" class="swiper-item" @click.stop="handleBack">
                <view class="image-container relative">
                    <image :src="item.url" mode="aspectFit" class="preview-image" @longpress="saveImage(item.url)">
                    </image>
                    <!-- 不合格图标 -->
                    <image :src="item.iconImg" class="unqualified-icon absolute right-4 w-16 h-16 z-10"></image>
                </view>
            </swiper-item>
        </swiper>

        <!-- 底部信息栏 -->
        <view class="info-bar">
            <view class="description">
                <text>{{ currentImage.description }}</text>
            </view>
            <view class="time">
                <text>{{ currentImage.time }}</text>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';

// 图片数据
const images = ref<any[]>([]);
const currentIndex = ref(0);

// 当前显示的图片信息
const currentImage = computed(() => {
    if (images.value.length === 0) return { url: '', description: '', time: '' };
    return images.value[currentIndex.value];
});

// 初始化数据
onLoad(() => {
    try {
        // 从缓存中获取图片数据
        const imageData = uni.getStorageSync('previewImageData');
        const initialIndex = uni.getStorageSync('previewCurrentIndex') || 0;

        if (imageData && Array.isArray(imageData)) {
            console.log('imageData', imageData)
            images.value = imageData;
            currentIndex.value = initialIndex;
        } else {
            uni.showToast({
                title: '获取图片数据失败',
                icon: 'none'
            });
            setTimeout(() => handleBack(), 1500);
        }
    } catch (error) {
        console.error('初始化预览页面失败:', error);
        handleBack();
    }
});

// 清理缓存
onUnload(() => {
    uni.removeStorageSync('previewImageData');
    uni.removeStorageSync('previewCurrentIndex');
});

// 处理轮播图变化
const handleSwiperChange = (e) => {
    currentIndex.value = e.detail.current;
};

// 返回上一页
const handleBack = () => {
    uni.navigateBack();
};

// 保存图片
const saveImage = (url) => {
    uni.showActionSheet({
        itemList: ['保存图片'],
        success: () => {
            uni.downloadFile({
                url: url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        uni.saveImageToPhotosAlbum({
                            filePath: res.tempFilePath,
                            success: () => {
                                uni.showToast({
                                    title: '保存成功',
                                    icon: 'success'
                                });
                            },
                            fail: () => {
                                uni.showToast({
                                    title: '保存失败',
                                    icon: 'none'
                                });
                            }
                        });
                    }
                },
                fail: () => {
                    uni.showToast({
                        title: '下载图片失败',
                        icon: 'none'
                    });
                }
            });
        }
    });
};
</script>

<style lang="scss" scoped>
.image-preview-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000;
    display: flex;
    flex-direction: column;
    z-index: 999;
}

.nav-bar {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 10;
}

.back-button {
    font-size: 18px;
    padding: 5px;
}

.counter {
    font-size: 14px;
    margin-left: -20rpx;
}

.swiper-container {
    flex: 1;
    width: 100%;
}

.swiper-item {
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-image {
    width: 100%;
    height: 100%;
}

.unqualified-icon {
    filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.3));
    top: 33%;
}

.info-bar {
    padding: 12px 15px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
}

.description {
    font-size: 14px;
    margin-bottom: 4px;
}

.time {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}
</style>